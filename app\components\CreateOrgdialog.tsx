"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogClose,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  <PERSON>alog<PERSON><PERSON>le,
  DialogTrigger,
  DialogDescription,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useOrganization } from "@/store/useOrganization";
import React, { FormEventHandler, useState } from "react";
import { z } from "zod";

// Client-side validation schema
const organizationFormSchema = z.object({
  name: z.string()
    .min(1, "Organization name is required")
    .max(100, "Organization name must be less than 100 characters")
    .regex(/^[a-zA-Z0-9\s\-]+$/, "Organization name can only contain letters, numbers, spaces, and hyphens"),
  slug: z.string()
    .min(1, "Slug is required")
    .max(50, "Slug must be less than 50 characters")
    .regex(/^[a-z0-9\-_]+$/, "Slug can only contain lowercase letters, numbers, hyphens, and underscores"),
  description: z.string()
    .max(500, "Description must be less than 500 characters")
    .optional()
});

interface CreateOrgDialogProps {
  triggerButton?: React.ReactNode;
  onSubmit: FormEventHandler<HTMLFormElement>;
  cancelButton?: React.ReactNode;
  submitButton?: React.ReactNode;
}

function CreateOrgdialog({
  triggerButton,
  onSubmit,
  cancelButton,
  submitButton,
}: CreateOrgDialogProps) {
  const { isCreateOrgOpen, setIsCreateOrgOpen, newOrgData, setNewOrgData } =
    useOrganization();

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateField = (field: string, value: any) => {
    try {
      if (field === 'name') {
        organizationFormSchema.shape.name.parse(value);
      } else if (field === 'slug') {
        organizationFormSchema.shape.slug.parse(value);
      } else if (field === 'description') {
        organizationFormSchema.shape.description.parse(value);
      }
      setErrors(prev => ({ ...prev, [field]: '' }));
    } catch (error) {
      if (error instanceof z.ZodError) {
        setErrors(prev => ({ ...prev, [field]: error.issues[0].message }));
      }
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    try {
      organizationFormSchema.parse(newOrgData);
      onSubmit(e);
      setErrors({});
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.issues.forEach(issue => {
          if (issue.path[0]) {
            newErrors[issue.path[0] as string] = issue.message;
          }
        });
        setErrors(newErrors);
      }
    }
  };

  const handleOpenChange = (open: boolean) => {
    setIsCreateOrgOpen(open);
    if (!open) {
      setErrors({});
    }
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9\-]/g, '')
      .trim();
  };

  return (
    <Dialog open={isCreateOrgOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <div className="w-full text-right">{triggerButton}</div>
      </DialogTrigger>
      <DialogContent aria-describedby="create-org-description">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Create New Organization</DialogTitle>
            <DialogDescription id="create-org-description">
              Fill out the form to create a new organization.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="org-name">Name</Label>
              <Input
                id="org-name"
                value={newOrgData.name}
                onChange={(e) => {
                  const value = e.target.value;
                  setNewOrgData({
                    ...newOrgData,
                    name: value,
                    slug: generateSlug(value)
                  });
                  validateField('name', value);
                }}
                placeholder="Enter organization name"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-500 mt-1">{errors.name}</p>
              )}
            </div>
            <div>
              <Label htmlFor="org-slug">Slug</Label>
              <Input
                id="org-slug"
                value={newOrgData.slug}
                onChange={(e) => {
                  const value = e.target.value.toLowerCase();
                  setNewOrgData({
                    ...newOrgData,
                    slug: value,
                  });
                  validateField('slug', value);
                }}
                placeholder="organization-slug"
                className={errors.slug ? "border-red-500" : ""}
              />
              {errors.slug && (
                <p className="text-sm text-red-500 mt-1">{errors.slug}</p>
              )}
            </div>
            <div>
              <Label htmlFor="org-description">Description</Label>
              <Textarea
                id="org-description"
                value={newOrgData.description}
                onChange={(e) => {
                  const value = e.target.value;
                  setNewOrgData({
                    ...newOrgData,
                    description: value,
                  });
                  validateField('description', value);
                }}
                placeholder="Enter organization description"
                rows={3}
                className={errors.description ? "border-red-500" : ""}
              />
              {errors.description && (
                <p className="text-sm text-red-500 mt-1">{errors.description}</p>
              )}
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>{cancelButton}</DialogClose>
            <Button 
              type="submit" 
              className="btn btn-primary"
              disabled={
                !newOrgData.name.trim() || 
                !newOrgData.slug.trim() || 
                Object.values(errors).some(error => error)
              }
            >
              {"Create organization"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export default CreateOrgdialog;
