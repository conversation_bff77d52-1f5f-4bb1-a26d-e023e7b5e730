import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { z } from "zod";

// Zod validation schemas
const createOrganizationSchema = z.object({
  name: z.string()
    .min(1, "Organization name is required")
    .max(100, "Organization name must be less than 100 characters")
    .regex(/^[a-zA-Z0-9\s\-_]+$/, "Organization name contains invalid characters")
    .transform(val => val.trim()),
  slug: z.string()
    .min(1, "Slug is required")
    .max(50, "Slug must be less than 50 characters")
    .regex(/^[a-z0-9\-_]+$/, "Slug can only contain lowercase letters, numbers, hyphens, and underscores")
    .transform(val => val.trim().toLowerCase()),
  description: z.string()
    .max(500, "Description must be less than 500 characters")
    .transform(val => val.trim())
});

export const createOrganization = mutation({
  args: {
    name: v.string(),
    slug: v.string(),
    description: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Validate inputs with Zod
    const validatedData = createOrganizationSchema.parse({
      name: args.name,
      slug: args.slug,
      description: args.description
    });

    // Check if slug already exists
    const existingOrg = await ctx.db
      .query("organizations")
      .withIndex("by_slug", (q) => q.eq("slug", validatedData.slug))
      .first();

    if (existingOrg) {
      throw new Error("An organization with this slug already exists");
    }

    const now = Date.now();
    const orgId = await ctx.db.insert("organizations", {
      ownerId: userId,
      name: validatedData.name,
      slug: validatedData.slug,
      description: validatedData.description,
      createdAt: now,
    });

    // Add creator as admin member
    await ctx.db.insert("organizationMembers", {
      organizationId: orgId,
      userId: userId,
      role: "admin",
      joinedAt: now,
    });

    return orgId;
  },
});

export const getUserOrganization = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const memberships = await ctx.db
      .query("organizationMembers")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    const organizations = await Promise.all(
      memberships.map(async (membership) => {
        const org = await ctx.db.get(membership.organizationId);
        if (!org) return null; // Only return if org exists
        return {
          ...org,
          role: membership.role,
        };
      })
    );

    return organizations.filter(Boolean);
  },
});

export const setActiveOrganization = mutation({
  args: {
    activeOrgId: v.id("organizations"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    // Remove any existing activeOrganization for this user
    const existing = await ctx.db
      .query("activeOrganization")
      .withIndex("by_owner", (q) => q.eq("ownerId", userId))
      .first();

    if (existing) {
      await ctx.db.delete(existing._id);
    }

    // Set new active organization
    await ctx.db.insert("activeOrganization", {
      ownerId: userId,
      activeOrgId: args.activeOrgId,
    });
  },
});

export const getActiveOrganization = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) return null;
    return await ctx.db
      .query("activeOrganization")
      .withIndex("by_owner", (q) => q.eq("ownerId", userId))
      .first();
  },
});

export const deleteOrganization = mutation({
  args: {
    organizationId: v.id("organizations"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    // Fetch the organization
    const org = await ctx.db.get(args.organizationId);
    if (!org) throw new Error("Organization not found");

    // Check if the user is the owner
    if (org.ownerId !== userId) {
      throw new Error("You are not authorized to delete this organization");
    }

    // Delete all organization members
    const members = await ctx.db
      .query("organizationMembers")
      .withIndex("by_organization", (q) =>
        q.eq("organizationId", args.organizationId)
      )
      .collect();
    for (const member of members) {
      await ctx.db.delete(member._id);
    }

    // Delete all activeOrganization records pointing to this org
    const activeOrgs = await ctx.db.query("activeOrganization").collect();
    for (const active of activeOrgs) {
      if (active.activeOrgId === args.organizationId) {
        await ctx.db.delete(active._id);
      }
    }

    // Delete all projects for this organization (if you have a projects table and index)
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_organization", (q) =>
        q.eq("organizationId", args.organizationId)
      )
      .collect();
    for (const project of projects) {
      await ctx.db.delete(project._id);
    }

    // Delete the organization itself
    await ctx.db.delete(args.organizationId);
  },
});
