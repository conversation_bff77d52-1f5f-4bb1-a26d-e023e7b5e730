import { create } from "zustand";

interface NewOrgData {
  name: string;
  description: string;
  slug: string;
}

interface UseOrgProps {
  isCreateOrgOpen: boolean;
  setIsCreateOrgOpen: (args: boolean) => void;
  selectedOrganizationId?: string;
  setSelectedOrganizationId: (id: string) => void;
  newOrgData: NewOrgData;
  setNewOrgData: (data: NewOrgData) => void;
}

export const useOrganization = create<UseOrgProps>((set, get) => ({
  isCreateOrgOpen: false,
  setIsCreateOrgOpen: (state) => set({ isCreateOrgOpen: state }),
  selectedOrganizationId: "",
  setSelectedOrganizationId: (id: string) =>
    set({ selectedOrganizationId: id }),

  newOrgData: { name: "", description: "", slug: "" },
  setNewOrgData: (data) => set({ newOrgData: data }),
}));
