import { Code } from "lucide-react";
import Link from "next/link";
import React from "react";
import { Button } from "../../components/ui/button";

function Navbar() {
  return (
    <header className="border-b bg-white">
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
            <Code className="w-5 h-5 text-white" />
          </div>
          <span className="text-xl font-bold text-gray-900">Reflow</span>
        </div>
        <nav className="hidden md:flex items-center space-x-8">
          <Link
            href="#features"
            className="text-gray-600 hover:text-gray-900 transition-colors"
          >
            Documentation
          </Link>
          <Link
            href="#pricing"
            className="text-gray-600 hover:text-gray-900 transition-colors"
          >
            Blog
          </Link>
          <Link
            href="#docs"
            className="text-gray-600 hover:text-gray-900 transition-colors"
          >
            Pricing
          </Link>
          <Link href="/login">
            <Button variant="ghost" className="cursor-pointer">
              Sign In
            </Button>
          </Link>
          <Link href="/auth/register">
            <Button className="cursor-pointer">Get Started</Button>
          </Link>
        </nav>
      </div>
    </header>
  );
}

export default Navbar;
