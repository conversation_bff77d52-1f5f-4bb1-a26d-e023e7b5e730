import { defineSchema, defineTable } from "convex/server";
import { authTables } from "@convex-dev/auth/server";
import { v } from "convex/values";

const applicationTables = {
  organizations: defineTable({
    ownerId: v.id("users"),
    name: v.string(),
    slug: v.string(),
    description: v.optional(v.string()),
    createdAt: v.number(),
  })
    .index("by_owner", ["ownerId"])
    .index("by_slug", ["slug"]),

  organizationMembers: defineTable({
    organizationId: v.id("organizations"),
    userId: v.id("users"),
    role: v.union(v.literal("admin"), v.literal("editor"), v.literal("viewer")),
    joinedAt: v.number(),
  })
    .index("by_organization", ["organizationId"])
    .index("by_user", ["userId"]),

  activeOrganization: defineTable({
    ownerId: v.id("users"),
    activeOrgId: v.id("organizations"),
  }).index("by_owner", ["ownerId"]),
  
  projects: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    organizationId: v.id("organizations"),
    createdBy: v.id("users"),
    isPublic: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_organization", ["organizationId"])
    .index("by_creator", ["createdBy"])
    .index("by_public", ["isPublic"]),

  components: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    projectId: v.id("projects"),
    createdBy: v.id("users"),
    version: v.string(),
    jsonSchema: v.string(), // JSON string of component structure
    propSchema: v.string(), // JSON string of prop definitions
    previewData: v.optional(v.string()), // JSON string of preview configuration
    isPublished: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_project", ["projectId"])
    .index("by_creator", ["createdBy"])
    .index("by_published", ["isPublished"]),

  componentVersions: defineTable({
    componentId: v.id("components"),
    version: v.string(),
    jsonSchema: v.string(),
    propSchema: v.string(),
    previewData: v.optional(v.string()),
    createdBy: v.id("users"),
    createdAt: v.number(),
  })
    .index("by_component", ["componentId"])
    .index("by_version", ["componentId", "version"]),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});
