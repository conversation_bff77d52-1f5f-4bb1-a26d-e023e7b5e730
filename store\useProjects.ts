import { create } from "zustand";

interface ProjectFormData {
  name: string;
  description: string;
  isPublic: boolean;
}

interface UseProjectsProps {
  isCreateProjectOpen: boolean;
  setIsCreateProjectOpen: (open: boolean) => void;
  projectFormData: ProjectFormData;
  setProjectFormData: (data: ProjectFormData) => void;
  resetProjectForm: () => void;
}

const initialFormData: ProjectFormData = {
  name: "",
  description: "",
  isPublic: false,
};

export const useProjects = create<UseProjectsProps>((set) => ({
  isCreateProjectOpen: false,
  setIsCreateProjectOpen: (open) => set({ isCreateProjectOpen: open }),
  
  projectFormData: initialFormData,
  setProjectFormData: (data) => set({ projectFormData: data }),
  resetProjectForm: () => set({ projectFormData: initialFormData }),
}));