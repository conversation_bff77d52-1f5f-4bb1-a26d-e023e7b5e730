"use client";

import React from "react";
import { Id } from "@/convex/_generated/dataModel";
import { ProjectsSidebar } from "../components/ProjectsSidebar";
import { useOrganization } from "@/store/useOrganization";
import { useProjects } from "@/store/useProjects";
import { projectsHooks } from "@/hooks/project";
import ProjectNavbar from "../components/ProjectNavbar";
import ProjectSort from "../components/ProjectSort";
import { Projectcard } from "../components/Projectcard";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { CreateProjectDialog } from "../components/CreateProjectDialog";
import { toast } from "sonner";

export default function ProjectsPage() {
  const { selectedOrganizationId } = useOrganization();
  const { setIsCreateProjectOpen } = useProjects();
  const { createProject, deleteProject, getProjectsByOrganization } = projectsHooks();
  
  const projects = getProjectsByOrganization(
    selectedOrganizationId as Id<"organizations"> | null
  );

  const handleCreateProject = () => {
    setIsCreateProjectOpen(true);
  };

  const handleCreateProjectSubmit = async (data: {
    name: string;
    description: string;
    isPublic: boolean;
  }) => {
    if (!selectedOrganizationId) {
      toast.error("Please select an organization first");
      return;
    }

    try {
      await createProject({
        name: data.name,
        description: data.description || undefined,
        organizationId: selectedOrganizationId as Id<"organizations">,
        isPublic: data.isPublic,
      });
      
      setIsCreateProjectOpen(false);
      toast.success("Project created successfully");
    } catch (error) {
      console.error("Failed to create project:", error);
      toast.error("Failed to create project");
    }
  };

  const handleDeleteProject = async (projectId: string) => {
    try {
      await deleteProject({ projectId: projectId as Id<"projects"> });
      toast.success("Project deleted successfully");
    } catch (error) {
      console.error("Failed to delete project:", error);
      toast.error("Failed to delete project");
    }
  };

  const handleEditProject = (projectId: string) => {
    console.log("Editing project:", projectId);
  };

  const handleDuplicateProject = (projectId: string) => {
    console.log("Duplicating project:", projectId);
  };

  return (
    <div className="flex min-h-screen">
      <ProjectsSidebar />
      <main className="flex-1">
        <ProjectNavbar />
        <ProjectSort />
        <div className="p-4">
          <div className="flex justify-between items-center mb-4">
            <h1 className="text-2xl font-semibold">Projects</h1>
            <Button onClick={handleCreateProject}>
              <Plus className="w-4 h-4 mr-2" />
              Create Project
            </Button>
          </div>
          <div className="flex flex-wrap gap-x-[15px] gap-y-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {projects?.map((project) => (
              <Projectcard
                key={project._id}
                project={project}
                onEdit={handleEditProject}
                onDelete={handleDeleteProject}
                onDuplicate={handleDuplicateProject}
              />
            ))}
          </div>
        </div>
      </main>
      
      <CreateProjectDialog onSubmit={handleCreateProjectSubmit} />
    </div>
  );
}
