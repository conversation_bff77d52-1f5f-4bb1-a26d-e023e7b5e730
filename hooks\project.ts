import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";

export const projectsHooks = () => {
  const getProjectsByOrganization = (organizationId: Id<"organizations"> | null) => {
    return useQuery(
      api.projects.getProjectsByOrganization,
      organizationId ? { organizationId } : "skip"
    );
  };

  const getProjectById = (projectId: Id<"projects">) => {
    return useQuery(api.projects.getProjectById, { projectId });
  };

  const createProject = useMutation(api.projects.createProject);
  const deleteProject = useMutation(api.projects.deleteProject);

  return {
    getProjectsByOrganization,
    getProjectById,
    createProject,
    deleteProject,
  };
};
