import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Input validation schemas
const projectNameSchema = {
  validate: (name: string) => {
    if (!name || typeof name !== 'string') {
      throw new Error("Project name is required");
    }
    if (name.trim().length === 0) {
      throw new Error("Project name cannot be empty");
    }
    if (name.length > 100) {
      throw new Error("Project name must be less than 100 characters");
    }
    if (!/^[a-zA-Z0-9\s\-_]+$/.test(name)) {
      throw new Error("Project name contains invalid characters");
    }
    return name.trim();
  }
};

const projectDescriptionSchema = {
  validate: (description?: string) => {
    if (!description) return undefined;
    if (typeof description !== 'string') {
      throw new Error("Description must be a string");
    }
    if (description.length > 500) {
      throw new Error("Description must be less than 500 characters");
    }
    return description.trim() || undefined;
  }
};

export const createProject = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    organizationId: v.id("organizations"),
    isPublic: v.boolean(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Validate and sanitize inputs
    const validatedName = projectNameSchema.validate(args.name);
    const validatedDescription = projectDescriptionSchema.validate(args.description);

    // Verify user is member of organization
    const membership = await ctx.db
      .query("organizationMembers")
      .withIndex("by_organization", (q) =>
        q.eq("organizationId", args.organizationId)
      )
      .filter((q) => q.eq(q.field("userId"), userId))
      .first();

    if (!membership) {
      throw new Error("Not a member of this organization");
    }

    // Check for duplicate project names within the organization
    const existingProject = await ctx.db
      .query("projects")
      .withIndex("by_organization", (q) =>
        q.eq("organizationId", args.organizationId)
      )
      .filter((q) => q.eq(q.field("name"), validatedName))
      .first();

    if (existingProject) {
      throw new Error("A project with this name already exists in the organization");
    }

    const now = Date.now();
    return await ctx.db.insert("projects", {
      name: validatedName,
      description: validatedDescription,
      organizationId: args.organizationId,
      createdBy: userId,
      isPublic: args.isPublic,
      createdAt: now,
      updatedAt: now,
    });
  },
});

export const deleteProject = mutation({
  args: {
    projectId: v.id("projects"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Project not found");
    }

    // Check if user has permission to delete
    const membership = await ctx.db
      .query("organizationMembers")
      .withIndex("by_organization", (q) =>
        q.eq("organizationId", project.organizationId)
      )
      .filter((q) => q.eq(q.field("userId"), userId))
      .first();

    if (
      !membership ||
      (membership.role !== "admin" && project.createdBy !== userId)
    ) {
      throw new Error("Not authorized to delete this project");
    }

    // Delete all components in the project
    const components = await ctx.db
      .query("components")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();

    for (const component of components) {
      // Delete component versions
      const versions = await ctx.db
        .query("componentVersions")
        .withIndex("by_component", (q) => q.eq("componentId", component._id))
        .collect();

      for (const version of versions) {
        await ctx.db.delete(version._id);
      }

      await ctx.db.delete(component._id);
    }

    await ctx.db.delete(args.projectId);
  },
});

export const getProjectsByOrganization = query({
  args: {
    organizationId: v.id("organizations"),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Verify user is member of organization
    const membership = await ctx.db
      .query("organizationMembers")
      .withIndex("by_organization", (q) =>
        q.eq("organizationId", args.organizationId)
      )
      .filter((q) => q.eq(q.field("userId"), userId))
      .first();

    if (!membership) {
      throw new Error("Not a member of this organization");
    }

    return await ctx.db
      .query("projects")
      .withIndex("by_organization", (q) =>
        q.eq("organizationId", args.organizationId)
      )
      .order("desc")
      .collect();
  },
});

export const getProjectById = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const project = await ctx.db.get(args.projectId);
    if (!project) {
      return null;
    }

    // Verify user has access to this project through organization membership
    const membership = await ctx.db
      .query("organizationMembers")
      .withIndex("by_organization", (q) =>
        q.eq("organizationId", project.organizationId)
      )
      .filter((q) => q.eq(q.field("userId"), userId))
      .first();

    if (!membership) {
      throw new Error("Not authorized to access this project");
    }

    return project;
  },
});
