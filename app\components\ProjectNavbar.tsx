import React from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Bell } from "lucide-react"; // Assuming you use lucide-react for icons

function ProjectNavbar() {
  return (
    <div className="p-4 h-[65px] flex items-center justify-between border-b dark:bg-slate-900 border-slate-200 dark:border-slate-700">
      {/* Search Bar */}
      <Input
        type="search"
        placeholder="Search projects..."
        className="max-w-xs"
      />

      {/* Notifications */}
      <Button variant="ghost" size="icon" aria-label="Notifications">
        <Bell className="w-5 h-5" />
        {/* You can add a badge or dot for unread notifications here */}
      </Button>
    </div>
  );
}

export default ProjectNavbar;
