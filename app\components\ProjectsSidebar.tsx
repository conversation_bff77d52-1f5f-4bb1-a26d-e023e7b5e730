"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ChevronLeft,
  ChevronRight,
  LogOut,
  Plus,
  Building2,
  User,
  Settings,
  Trash,
} from "lucide-react";

import { toast } from "sonner";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAuthActions } from "@convex-dev/auth/react";
import { useMutation, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { organizationHooks } from "@/hooks/organization";
import { useOrganization } from "@/store/useOrganization";
import { Id } from "@/convex/_generated/dataModel";
import CreateOrgdialog from "./CreateOrgdialog";

export function ProjectsSidebar() {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const [user, setUser] = useState<any>(null);

  const router = useRouter();

  const { signOut } = useAuthActions();

  const {
    createOrganization,
    getorganizations,
    setActiveOrganization,
    getActiveOrganization,
    deleteOrganization,
  } = organizationHooks();

  const {
    isCreateOrgOpen,
    setIsCreateOrgOpen,
    selectedOrganizationId,
    setSelectedOrganizationId,
    newOrgData,
    setNewOrgData,
  } = useOrganization();

  const [orgIdInput, setOrgIdInput] = useState("");

  useEffect(() => {
    console.log(getorganizations);
    if (
      getActiveOrganization &&
      getActiveOrganization.activeOrgId &&
      selectedOrganizationId !== getActiveOrganization.activeOrgId
    ) {
      setSelectedOrganizationId(
        getActiveOrganization.activeOrgId as Id<"organizations">
      );
    }
    // Optionally, fallback to first org if no activeOrgId is set
    else if (
      !selectedOrganizationId &&
      getorganizations.length > 0 &&
      (!getActiveOrganization || !getActiveOrganization.activeOrgId)
    ) {
      setSelectedOrganizationId(
        getorganizations[0]?._id as Id<"organizations">
      );
    }
  }, [
    getorganizations,
    getActiveOrganization,
    selectedOrganizationId,
    setSelectedOrganizationId,
  ]);

  const handleLogout = async () => {
    try {
      await signOut();
      router.push("/login");
      console.log("Logged out");
    } catch (error) {
      console.log(error);
    }
  };

  const handleCreateOrganization = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log(newOrgData);
    try {
      await createOrganization({
        name: newOrgData.name.trim(),
        slug: newOrgData.slug.trim(),
        description: newOrgData.description.trim(),
      });

      setNewOrgData({ name: "", description: "", slug: "" });

      setIsCreateOrgOpen(false);

      toast("New Organization Created");
    } catch (error) {
      console.log(e);
      toast("Please try again.");
    } finally {
    }
  };

  const handleDeleteOrganization = async () => {
    if (!selectedOrganizationId) return;
    try {
      await deleteOrganization({
        organizationId: selectedOrganizationId as Id<"organizations">,
      });

      setOrgIdInput("");

      // Optionally, reset selectedOrganizationId or show a toast
    } catch (error) {
      // Handle error (e.g., show a toast)
      console.error(error);
    }
  };

  return (
    <div
      className={`bg-white dark:bg-slate-900 border-r border-slate-200 dark:border-slate-700 transition-all duration-300 ${
        isCollapsed ? "w-16" : "w-55"
      }`}
    >
      <div className="flex flex-col h-full">
        {/* Render CreateOrgdialog ONCE at the top level so it can be triggered from anywhere */}
        <CreateOrgdialog
          onSubmit={handleCreateOrganization}
          triggerButton={null}
          cancelButton={<Button variant="outline">Cancel</Button>}
          submitButton={<Button type="submit">Create organization</Button>}
        />
        {/* Header */}
        <div className="p-4 border-b border-slate-200 dark:border-slate-700 h-[65px]">
          <div className="flex items-center justify-between">
            {!isCollapsed && (
              <div className="flex items-center space-x-2">
                <Building2 className="w-6 h-6 text-blue-600" />
                <span className="font-semibold text-slate-900 dark:text-white">
                  Reflow
                </span>
              </div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="ml-auto"
            >
              {isCollapsed ? (
                <ChevronRight className="w-4 h-4" />
              ) : (
                <ChevronLeft className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Organization Selector */}
        <div className="p-4 border-b border-slate-200 dark:border-slate-700">
          {!isCollapsed ? (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  Organization
                </span>
                {/* Remove CreateOrgdialog here, use a button to open the dialog instead */}
                <Button
                  variant="ghost"
                  onClick={() => setIsCreateOrgOpen(true)}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>

              <div className="flex justify-between">
                <Select
                  value={selectedOrganizationId}
                  onValueChange={(orgId) => {
                    setSelectedOrganizationId(orgId);
                    setActiveOrganization({
                      activeOrgId: orgId as Id<"organizations">,
                    });
                  }}
                >
                  <SelectTrigger className="w-full focus-visible:ring-[0px]">
                    <SelectValue
                      className="text-black"
                      placeholder="Select organization"
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {getorganizations.map((org: any) => (
                      <SelectItem key={org._id} value={org._id}>
                        {org.name}
                      </SelectItem>
                    ))}
                    {/* Add a custom item for creating a new organization */}
                    <div className="px-2 py-1 border-t border-slate-200 dark:border-slate-700">
                      <Button
                        variant="ghost"
                        className="w-full flex items-center justify-start"
                        onClick={() => setIsCreateOrgOpen(true)}
                        type="button"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Create Organization
                      </Button>
                    </div>
                  </SelectContent>
                </Select>
                {/* {getorganizations && getorganizations.length > 0 && (
                  <Dialog>
                    <form>
                      <DialogTrigger asChild>
                        <Button variant="outline">
                          <Trash size={20} color="red" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                          <DialogTitle>Delete Organization?</DialogTitle>
                          <DialogDescription>
                            Deleting this organization will also remove all the
                            related entities such as Organization Members and
                            Projects
                          </DialogDescription>
                          <DialogDescription>
                            Copy id to delete:{" "}
                            <span className="font-mono text-red-400">
                              {selectedOrganizationId}
                            </span>
                          </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4">
                          <div className="grid gap-3">
                            <Label htmlFor="orgId">Organization ID</Label>
                            <Input
                              value={orgIdInput}
                              onChange={(e) => setOrgIdInput(e.target.value)}
                              placeholder="Enter organization ID to confirm"
                            />
                          </div>
                        </div>
                        <DialogFooter>
                          <DialogClose asChild>
                            <Button variant="outline">Cancel</Button>
                          </DialogClose>
                          <DialogClose asChild>
                            <Button
                              type="submit"
                              onClick={handleDeleteOrganization}
                              disabled={orgIdInput !== selectedOrganizationId}
                              className="bg-red-500"
                            >
                              Delete
                            </Button>
                          </DialogClose>
                        </DialogFooter>
                      </DialogContent>
                    </form>
                  </Dialog>
                )} */}
              </div>
            </div>
          ) : (
            <div className="flex justify-center">
              <Avatar className="w-8 h-8">
                <AvatarImage src={""} />
                <AvatarFallback className="text-xs"></AvatarFallback>
              </Avatar>
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="flex-1 p-4">
          {!isCollapsed && (
            <div className="space-y-2">
              <Button
                variant="ghost"
                className="w-full justify-start"
                onClick={() => router.push("/dashboard")}
              >
                <User className="w-4 h-4 mr-2" />
                Dashboard
              </Button>
              <Button
                variant="ghost"
                className="w-full justify-start"
                onClick={() => router.push("/settings")}
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </div>
          )}
        </div>

        {/* User Profile */}
        <div className="p-4 border-t border-slate-200 dark:border-slate-700">
          {!isCollapsed ? (
            <div className="flex items-center space-x-3">
              <Avatar className="w-8 h-8">
                <AvatarImage src={user?.user_metadata?.avatar_url} />
                <AvatarFallback className="text-sm">A</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-slate-900 dark:text-white truncate">
                  {user?.user_metadata?.full_name || user?.email}
                </p>
                <p className="text-xs text-slate-500 dark:text-slate-400 truncate">
                  {user?.email}
                </p>
              </div>
              <Button variant="ghost" size="sm" onClick={handleLogout}>
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          ) : (
            <div className="flex justify-center">
              <Button variant="ghost" size="sm" onClick={handleLogout}>
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
