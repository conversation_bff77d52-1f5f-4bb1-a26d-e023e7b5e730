"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  <PERSON>alogClose,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { useProjects } from "@/store/useProjects";
import { z } from "zod";

// Client-side validation schema
const projectFormSchema = z.object({
  name: z.string()
    .min(1, "Project name is required")
    .max(100, "Project name must be less than 100 characters")
    .regex(/^[a-zA-Z0-9\s\-_]+$/, "Project name contains invalid characters"),
  description: z.string()
    .max(500, "Description must be less than 500 characters")
    .optional(),
  isPublic: z.boolean()
});

interface CreateProjectDialogProps {
  onSubmit: (data: {
    name: string;
    description: string;
    isPublic: boolean;
  }) => void;
}

export function CreateProjectDialog({ onSubmit }: CreateProjectDialogProps) {
  const {
    isCreateProjectOpen,
    setIsCreateProjectOpen,
    projectFormData,
    setProjectFormData,
    resetProjectForm,
  } = useProjects();

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateField = (field: string, value: any) => {
    try {
      if (field === 'name') {
        projectFormSchema.shape.name.parse(value);
      } else if (field === 'description') {
        projectFormSchema.shape.description.parse(value);
      }
      setErrors(prev => ({ ...prev, [field]: '' }));
    } catch (error) {
      if (error instanceof z.ZodError) {
        setErrors(prev => ({ ...prev, [field]: error.issues[0].message }));
      }
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const validatedData = projectFormSchema.parse(projectFormData);
      onSubmit({
        name: validatedData.name.trim(),
        description: validatedData.description?.trim() || '',
        isPublic: validatedData.isPublic
      });
      resetProjectForm();
      setErrors({});
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        error.issues.forEach(issue => {
          if (issue.path[0]) {
            newErrors[issue.path[0] as string] = issue.message;
          }
        });
        setErrors(newErrors);
      }
    }
  };

  const handleOpenChange = (open: boolean) => {
    setIsCreateProjectOpen(open);
    if (!open) {
      resetProjectForm();
      setErrors({});
    }
  };

  return (
    <Dialog open={isCreateProjectOpen} onOpenChange={handleOpenChange}>
      <DialogContent>
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Create New Project</DialogTitle>
            <DialogDescription>
              Create a new project in your organization.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="project-name">Name</Label>
              <Input
                id="project-name"
                value={projectFormData.name}
                onChange={(e) => {
                  const value = e.target.value;
                  setProjectFormData({ ...projectFormData, name: value });
                  validateField('name', value);
                }}
                placeholder="Enter project name"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-500 mt-1">{errors.name}</p>
              )}
            </div>
            <div>
              <Label htmlFor="project-description">Description</Label>
              <Textarea
                id="project-description"
                value={projectFormData.description}
                onChange={(e) => {
                  const value = e.target.value;
                  setProjectFormData({ ...projectFormData, description: value });
                  validateField('description', value);
                }}
                placeholder="Enter project description (optional)"
                rows={3}
                className={errors.description ? "border-red-500" : ""}
              />
              {errors.description && (
                <p className="text-sm text-red-500 mt-1">{errors.description}</p>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="project-public"
                checked={projectFormData.isPublic}
                onCheckedChange={(checked) =>
                  setProjectFormData({ ...projectFormData, isPublic: checked })
                }
              />
              <Label htmlFor="project-public">Make project public</Label>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button 
              type="submit" 
              disabled={!projectFormData.name.trim() || Object.values(errors).some(error => error)}
            >
              Create Project
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}



