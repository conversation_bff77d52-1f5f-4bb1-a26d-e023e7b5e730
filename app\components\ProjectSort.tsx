import React from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

function ProjectSort() {
  return (
    <div className="flex justify-end p-4">
      <div>
        <Select>
          <SelectTrigger className="w-[150px] focus-visible:ring-[0px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Sort by</SelectLabel>
              <SelectItem value="Alphabetical">Alphabetical</SelectItem>
              <SelectItem value="Date created">Date created</SelectItem>
              <SelectItem value="blueberry">Last modified</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}

export default ProjectSort;
