"use client"

import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from "@/components/ui/context-menu";
import { Doc } from "@/convex/_generated/dataModel";
import { formatDistanceToNow } from "date-fns";
import { Edit, Trash2, Copy, ExternalLink } from "lucide-react";
import { useRouter } from "next/navigation";

interface ProjectcardProps {
  project: Doc<"projects">;
  onEdit?: (projectId: string) => void;
  onDelete?: (projectId: string) => void;
  onDuplicate?: (projectId: string) => void;
  onOpen?: (projectId: string) => void;
}

export function Projectcard({ 
  project, 
  onEdit, 
  onDelete, 
  onDuplicate, 
  onOpen 
}: ProjectcardProps) {
  const router = useRouter();
  
  const formatDate = (timestamp: number) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  };

  const handleProjectClick = () => {
    router.push(`/workspace/${project._id}`);
  };

  const handleOpenProject = () => {
    router.push(`/workspace/${project._id}`);
  };

  return (
    <ContextMenu>
      <ContextMenuTrigger>
        <div 
          className="min-w-55 max-w-65 border-2 rounded-[8px] shadow-mg border-slate-100 cursor-pointer hover:shadow-lg transition-shadow"
          onClick={handleProjectClick}
        >
          <div
            className="border-b border-gray-200"
            style={{
              backgroundColor: "#fff",
              backgroundImage: `
              linear-gradient(45deg, #d6d6d6 25%, transparent 25%, 
                transparent 75%, #d6d6d6 75%, #d6d6d6),
              linear-gradient(45deg, #d6d6d6 25%, transparent 25%, 
                transparent 75%, #d6d6d6 75%, #d6d6d6)`,
              backgroundSize: "20px 20px",
              backgroundPosition: "0 0, 10px 10px",
              width: "auto",
              height: "110px",
              borderTopLeftRadius: 5,
              borderTopRightRadius: 5,
            }}
          />
          <div className="p-4">
            <h3 className="font-semibold text-gray-900 mb-1">{project.name}</h3>
            <p className="text-sm text-gray-600 mb-2 line-clamp-2">
              {project.description || "No description"}
            </p>
            <p className="text-xs text-gray-500">
              Updated {formatDate(project.updatedAt)}
            </p>
          </div>
        </div>
      </ContextMenuTrigger>
      <ContextMenuContent>
        <ContextMenuItem onClick={handleOpenProject}>
          <ExternalLink className="w-4 h-4 mr-2" />
          Open Project
        </ContextMenuItem>
        <ContextMenuItem onClick={() => onEdit?.(project._id)}>
          <Edit className="w-4 h-4 mr-2" />
          Edit
        </ContextMenuItem>
        <ContextMenuItem onClick={() => onDuplicate?.(project._id)}>
          <Copy className="w-4 h-4 mr-2" />
          Duplicate
        </ContextMenuItem>
        <ContextMenuItem 
          onClick={() => onDelete?.(project._id)}
          className="text-red-600"
        >
          <Trash2 className="w-4 h-4 mr-2" />
          Delete
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
}
