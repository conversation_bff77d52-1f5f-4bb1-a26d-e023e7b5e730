"use client";

import React from "react";
import { useParams } from "next/navigation";
import { Id } from "@/convex/_generated/dataModel";
import { projectsHooks } from "@/hooks/project";

export default function ComponentBuilderPage() {
  const params = useParams();
  const projectId = params.projectId as Id<"projects">;
  const { getProjectById } = projectsHooks();
  
  const project = getProjectById(projectId);

  if (!project) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-semibold mb-2">Project not found</h1>
          <p className="text-gray-600">The project you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">{project.name}</h1>
            <p className="text-sm text-gray-600">{project.description}</p>
          </div>
          <div className="flex items-center space-x-4">
            <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Save
            </button>
            <button className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
              Preview
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-73px)]">
        {/* Sidebar - Component Library */}
        <aside className="w-64 bg-white border-r border-gray-200 overflow-y-auto">
          <div className="p-4">
            <h2 className="text-lg font-medium mb-4">Components</h2>
            <div className="space-y-2">
              <div className="p-3 border border-gray-200 rounded-md cursor-pointer hover:bg-gray-50">
                <div className="font-medium">Button</div>
                <div className="text-sm text-gray-500">Interactive button component</div>
              </div>
              <div className="p-3 border border-gray-200 rounded-md cursor-pointer hover:bg-gray-50">
                <div className="font-medium">Input</div>
                <div className="text-sm text-gray-500">Text input field</div>
              </div>
              <div className="p-3 border border-gray-200 rounded-md cursor-pointer hover:bg-gray-50">
                <div className="font-medium">Card</div>
                <div className="text-sm text-gray-500">Content container</div>
              </div>
            </div>
          </div>
        </aside>

        {/* Canvas Area */}
        <main className="flex-1 bg-gray-100 p-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
            <div className="p-8 h-full flex items-center justify-center">
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Start Building Your Component
                </h3>
                <p className="text-gray-600 mb-4">
                  Drag components from the sidebar to start designing
                </p>
                <div className="w-16 h-16 bg-gray-200 rounded-lg mx-auto"></div>
              </div>
            </div>
          </div>
        </main>

        {/* Properties Panel */}
        <aside className="w-64 bg-white border-l border-gray-200 overflow-y-auto">
          <div className="p-4">
            <h2 className="text-lg font-medium mb-4">Properties</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Width
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="auto"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Height
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="auto"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Background
                </label>
                <input
                  type="color"
                  className="w-full h-10 border border-gray-300 rounded-md"
                />
              </div>
            </div>
          </div>
        </aside>
      </div>
    </div>
  );
}