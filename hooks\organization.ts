import { api } from "@/convex/_generated/api";
import { mutation } from "@/convex/_generated/server";
import { useMutation, useQuery } from "convex/react";

export const organizationHooks = () => {
  //create organization
  const createOrganization = useMutation(api.organizations.createOrganization);

  //get organization
  const getorganizations =
    useQuery(api.organizations.getUserOrganization) || [];

  const setActiveOrganization = useMutation(
    api.organizations.setActiveOrganization
  );

  const getActiveOrganization = useQuery(
    api.organizations.getActiveOrganization
  );

  const deleteOrganization = useMutation(api.organizations.deleteOrganization);

  return {
    createOrganization,
    getorganizations,
    setActiveOrganization,
    getActiveOrganization,
    deleteOrganization,
  };
};
